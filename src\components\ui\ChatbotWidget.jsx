import { useEffect, useState } from "react";

/**
 * JotForm AI Chatbot Widget Component
 *
 * This component integrates the JotForm AI chatbot into the UrbanEdge real estate application.
 * The chatbot provides AI-powered assistance for real estate inquiries and property searches.
 *
 * Features:
 * - Dynamic script loading for optimal performance
 * - Configurable widget settings (skipWelcome, maximizable)
 * - Error handling and loading states
 * - Responsive design integration
 * - Cross-page availability
 *
 * Configuration:
 * - skipWelcome=1: Skips the initial welcome message
 * - maximizable=1: Allows the chat widget to be maximized
 * - Positioned as a floating widget in the bottom-right corner
 *
 * @returns {JSX.Element} The chatbot widget component
 */
const ChatbotWidget = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Check if script is already loaded
    if (document.querySelector('script[src*="jotform.com/jsform"]')) {
      setIsLoaded(true);
      return;
    }

    // Create and configure the script element
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.src = "https://form.jotform.com/jsform/243488267442465";
    script.async = true;

    // Handle successful script loading
    script.onload = () => {
      setIsLoaded(true);
      console.log("JotForm AI Chatbot loaded successfully");
    };

    // Handle script loading errors
    script.onerror = () => {
      setHasError(true);
      console.error("Failed to load JotForm AI Chatbot");
    };

    // Append script to document head
    document.head.appendChild(script);

    // Cleanup function to remove script on component unmount
    return () => {
      const existingScript = document.querySelector('script[src*="jotform.com/jsform"]');
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  // Don't render anything if there's an error
  if (hasError) {
    return null;
  }

  return (
    <>
      {/* JotForm AI Chatbot Container */}
      <div
        id="jotform-chatbot-container"
        className="fixed bottom-4 right-4 z-50"
        style={{
          // Ensure the widget doesn't interfere with other UI elements
          pointerEvents: isLoaded ? 'auto' : 'none',
          opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out'
        }}
      >
        {/* The actual chatbot will be injected here by JotForm's script */}
        <iframe
          id="JotFormIFrame-243488267442465"
          title="UrbanEdge AI Assistant"
          onLoad={() => setIsLoaded(true)}
          allowTransparency="true"
          allowFullScreen={true}
          allow="geolocation; microphone; camera"
          src="https://form.jotform.com/243488267442465?skipWelcome=1&maximizable=1"
          frameBorder="0"
          style={{
            minWidth: '100%',
            maxWidth: '100%',
            height: '539px',
            border: 'none'
          }}
          scrolling="no"
        />
      </div>

      {/* Loading indicator */}
      {!isLoaded && !hasError && (
        <div className="fixed bottom-4 right-4 z-40">
          <div className="bg-white dark:bg-brown-dark rounded-lg shadow-lg p-3">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-taupe"></div>
              <span className="text-sm text-brown dark:text-beige-medium">
                Loading AI Assistant...
              </span>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ChatbotWidget;
