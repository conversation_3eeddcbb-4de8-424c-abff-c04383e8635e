/* eslint-disable react/prop-types */
import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Heart, MapPin, Bed, Bath, Square, Loader2 } from "lucide-react";
import { formatPropertyPrice } from "../../utils/currencyUtils";
import { favoritesService } from "../../lib/favoritesService";
import { useAuth } from "../../contexts/AuthContext";
import { Card, CardContent } from "../ui/card";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";

const PropertyCard = ({ property, onFavoriteToggle }) => {
  const { user } = useAuth();
  const [isFavorite, setIsFavorite] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Check if property is favorited when component mounts or user changes
  useEffect(() => {
    const checkFavoriteStatus = async () => {
      if (user && property.id) {
        const { data } = await favoritesService.isPropertyFavorited(property.id);
        setIsFavorite(data);
      } else {
        setIsFavorite(false);
      }
    };

    checkFavoriteStatus();
  }, [user, property.id]);

  const toggleFavorite = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('Heart icon clicked for property:', property.id);
    console.log('Current favorite status:', isFavorite);
    console.log('Current user:', user?.id);

    // Require authentication for favorites
    if (!user) {
      alert('Please sign in to add properties to your favorites');
      return;
    }

    setIsLoading(true);

    try {
      console.log('Calling toggleFavorite service...');
      const result = await favoritesService.toggleFavorite(property.id, isFavorite);
      console.log('Toggle favorite result:', result);

      if (result.error) {
        console.error('Error toggling favorite:', result.error);
        alert('Failed to update favorites. Please try again.');
      } else {
        console.log('Successfully toggled favorite, updating UI state');
        setIsFavorite(!isFavorite);

        // Call the callback if provided
        if (onFavoriteToggle) {
          onFavoriteToggle();
        }
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      alert('Failed to update favorites. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get the first image URL
  const getImageUrl = () => {
    // Handle different image URL field names
    if (property.thumbnail_url) return property.thumbnail_url;
    if (property.imageUrl) return property.imageUrl;
    if (property.images && property.images.length > 0) {
      return property.images[0].url || property.images[0].image_url;
    }
    if (property.property_images && property.property_images.length > 0) {
      return property.property_images[0].image_url || property.property_images[0].url;
    }
    return "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80";
  };

  // Helper function to clean string values (remove extra quotes)
  const cleanString = (str) => {
    if (!str) return "";
    return str.toString().replace(/^"|"$/g, '');
  };



  // Helper function to safely format square feet
  const formatSquareFeet = () => {
    const sqft = property.sqft || property.square_feet;
    if (sqft && !isNaN(sqft)) {
      return sqft.toLocaleString();
    }
    return "N/A";
  };

  return (
    <Card className="group overflow-hidden hover:shadow-lg transition-shadow duration-300">
      {/* Image Container */}
      <div className="relative overflow-hidden h-48 xs:h-56 sm:h-64">
        <img
          src={getImageUrl()}
          alt={property.title || "Property"}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          onError={(e) => {
            e.target.onerror = null;
            e.target.src = "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80";
          }}
        />

        {/* Price Badge */}
        <Badge className="absolute top-2 xs:top-4 left-2 xs:left-4 bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light border-0 shadow-md font-heading font-bold text-sm xs:text-base">
          {formatPropertyPrice(property.price)}
          {(property.isRental || property.sale_type === "For Rent") && (
            <span className="text-xs xs:text-sm font-normal">/mo</span>
          )}
        </Badge>

        {/* Favorite Button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleFavorite}
          disabled={isLoading}
          className={`absolute top-2 xs:top-4 right-2 xs:right-4 h-8 w-8 xs:h-10 xs:w-10 bg-white dark:bg-brown-dark rounded-full shadow-md hover:shadow-lg transition-all duration-200 ${
            isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'
          }`}
          aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 xs:h-5 xs:w-5 animate-spin" />
          ) : (
            <Heart
              className={`h-4 w-4 xs:h-5 xs:w-5 transition-colors ${
                isFavorite
                  ? 'text-red-500 fill-red-500'
                  : 'text-brown-dark dark:text-beige-light hover:text-red-500'
              }`}
            />
          )}
        </Button>

        {/* Property Type Badge */}
        {(property.type || property.property_type) && (
          <Badge className="absolute bottom-2 xs:bottom-4 left-2 xs:left-4 bg-taupe text-white border-0">
            {cleanString(property.type || property.property_type)}
          </Badge>
        )}
      </div>

      {/* Content */}
      <CardContent className="p-3 xs:p-4">
        <h3 className="font-heading font-bold text-base xs:text-lg sm:text-xl mb-1 xs:mb-2 text-brown-dark dark:text-beige-light line-clamp-1">
          {cleanString(property.title) || "Property"}
        </h3>

        {/* Location */}
        <div className="flex items-center mb-2 xs:mb-3 text-brown dark:text-beige-medium">
          <MapPin className="h-3 w-3 xs:h-4 xs:w-4 mr-1 flex-shrink-0" />
          <span className="text-xs xs:text-sm truncate">
            {cleanString(property.location) || "Location not specified"}
          </span>
        </div>

        {/* Features */}
        <div className="flex justify-between mb-3 xs:mb-4 text-brown dark:text-beige-medium">
          <div className="flex items-center">
            <Bed className="h-3 w-3 xs:h-4 xs:w-4 mr-1" />
            <span className="text-xs xs:text-sm">{property.bedrooms || 0} Beds</span>
          </div>
          <div className="flex items-center">
            <Bath className="h-3 w-3 xs:h-4 xs:w-4 mr-1" />
            <span className="text-xs xs:text-sm">
              {property.bathrooms || 0} Baths
            </span>
          </div>
          <div className="flex items-center">
            <Square className="h-3 w-3 xs:h-4 xs:w-4 mr-1" />
            <span className="text-xs xs:text-sm">
              {formatSquareFeet()} sqft
            </span>
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <Button variant="outline" asChild className="h-8 xs:h-10 text-xs xs:text-sm">
            <Link to={`/properties/${property.id}`}>
              View Details
            </Link>
          </Button>
          {property.latitude && property.longitude && (
            <Button variant="outline" asChild className="h-8 xs:h-10 text-xs xs:text-sm">
              <Link to={`/properties?lat=${property.latitude}&lng=${property.longitude}&zoom=15&property=${property.id}`}>
                View on Map
              </Link>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PropertyCard;
