{"name": "StarterPackCode", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.1.10", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^5.1.1", "@material-tailwind/react": "^2.1.10", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.76.1", "@tanstack/react-table": "^8.21.3", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.5.4", "leaflet": "^1.9.4", "lucide-react": "^0.503.0", "openai": "^5.5.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.58.1", "react-icons": "^5.3.0", "react-leaflet": "^4.2.1", "react-leaflet-cluster": "^2.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.27.0", "react-router-hash-link": "^2.4.3", "react-simple-typewriter": "^5.0.1", "react-slick": "^0.30.2", "react-toggle-dark-mode": "^1.1.1", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "slick-carousel": "^1.8.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7", "vite": "^5.4.8"}}