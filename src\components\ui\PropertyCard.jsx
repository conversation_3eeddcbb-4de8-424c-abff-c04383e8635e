import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import { 
  MapPinIcon, 
  HeartIcon,
  BedIcon,
  BathIcon,
  HomeIcon
} from "@heroicons/react/24/outline";
import { HeartIcon as HeartSolidIcon } from "@heroicons/react/24/solid";
import { useAuth } from "../../contexts/AuthContext";
import { favoritesService } from "../../lib/favoritesService";

const PropertyCard = ({ property, showFavorite = true }) => {
  const { user } = useAuth();
  const [isFavorite, setIsFavorite] = useState(property?.is_favorite || false);
  const [isLoading, setIsLoading] = useState(false);

  const formatPrice = (price) => {
    if (!price) return "Price on request";
    return new Intl.NumberFormat("en-NG", {
      style: "currency",
      currency: "NGN",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const handleFavoriteToggle = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!user) {
      // Redirect to login or show login modal
      return;
    }

    setIsLoading(true);
    try {
      if (isFavorite) {
        await favoritesService.removeFavorite(property.id);
        setIsFavorite(false);
      } else {
        await favoritesService.addFavorite(property.id);
        setIsFavorite(true);
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageError = (e) => {
    e.target.src = "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80";
  };

  if (!property) {
    return (
      <div className="card animate-pulse">
        <div className="h-48 bg-beige-medium dark:bg-brown"></div>
        <div className="p-4 space-y-3">
          <div className="h-4 bg-beige-medium dark:bg-brown rounded"></div>
          <div className="h-4 bg-beige-medium dark:bg-brown rounded w-3/4"></div>
          <div className="h-4 bg-beige-medium dark:bg-brown rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  const primaryImage = property.images?.[0]?.image_url || property.image_url;

  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
      className="card group"
    >
      <Link to={`/properties/${property.id}`} className="block">
        {/* Image */}
        <div className="relative h-48 overflow-hidden">
          <img
            src={primaryImage}
            alt={property.title}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            onError={handleImageError}
          />
          
          {/* Favorite Button */}
          {showFavorite && user && (
            <button
              onClick={handleFavoriteToggle}
              disabled={isLoading}
              className="absolute top-3 right-3 p-2 bg-white/80 dark:bg-brown-dark/80 rounded-full hover:bg-white dark:hover:bg-brown-dark transition-colors duration-200"
            >
              {isFavorite ? (
                <HeartSolidIcon className="h-5 w-5 text-red-500" />
              ) : (
                <HeartIcon className="h-5 w-5 text-brown-dark dark:text-beige-light" />
              )}
            </button>
          )}

          {/* Property Type Badge */}
          <div className="absolute top-3 left-3">
            <span className="bg-taupe text-white px-2 py-1 rounded-md text-xs font-medium capitalize">
              {property.property_types?.name || property.type || "Property"}
            </span>
          </div>

          {/* Sale Type Badge */}
          <div className="absolute bottom-3 left-3">
            <span className="bg-brown-dark text-white px-2 py-1 rounded-md text-xs font-medium capitalize">
              For {property.sale_types?.name || property.sale_type || "Sale"}
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Price */}
          <div className="mb-2">
            <span className="text-lg font-bold text-taupe">
              {formatPrice(property.price)}
            </span>
            {property.sale_types?.name === "rent" && (
              <span className="text-sm text-brown dark:text-beige-medium">/month</span>
            )}
          </div>

          {/* Title */}
          <h3 className="font-heading font-semibold text-brown-dark dark:text-beige-light mb-2 line-clamp-2 group-hover:text-taupe transition-colors duration-200">
            {property.title}
          </h3>

          {/* Location */}
          <div className="flex items-center text-brown dark:text-beige-medium mb-3">
            <MapPinIcon className="h-4 w-4 mr-1 flex-shrink-0" />
            <span className="text-sm line-clamp-1">{property.location}</span>
          </div>

          {/* Property Details */}
          <div className="flex items-center justify-between text-sm text-brown dark:text-beige-medium">
            <div className="flex items-center space-x-4">
              {property.bedrooms && (
                <div className="flex items-center">
                  <BedIcon className="h-4 w-4 mr-1" />
                  <span>{property.bedrooms}</span>
                </div>
              )}
              {property.bathrooms && (
                <div className="flex items-center">
                  <BathIcon className="h-4 w-4 mr-1" />
                  <span>{property.bathrooms}</span>
                </div>
              )}
              {property.area && (
                <div className="flex items-center">
                  <HomeIcon className="h-4 w-4 mr-1" />
                  <span>{property.area} sqft</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export default PropertyCard;
